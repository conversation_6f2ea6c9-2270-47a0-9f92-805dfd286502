package service

import (
	"context"
	"fmt"
	"math"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	repositoryErrors "pxpat-backend/pkg/errors/repository"
)

// FavoriteItemService 收藏项服务实现
type FavoriteItemService struct {
	favoriteFolderRepo repository.FavoriteFolderRepository
	favoriteItemRepo   repository.FavoriteItemRepository
	favoriteStatsRepo  repository.FavoriteStatsRepository
	likeRepo           repository.LikeRepository
	userClient         client.UserServiceClient
	videoClient        client.VideoServiceClient
	novelClient        client.NovelServiceClient
	musicClient        client.MusicServiceClient
}

// NewFavoriteItemService 创建收藏项服务实例
func NewFavoriteItemService(
	favoriteFolderRepo repository.FavoriteFolderRepository,
	favoriteItemRepo repository.FavoriteItemRepository,
	favoriteStatsRepo repository.FavoriteStatsRepository,
	likeRepo repository.LikeRepository,
	userClient client.UserServiceClient,
	videoClient client.VideoServiceClient,
	novelClient client.NovelServiceClient,
	musicClient client.MusicServiceClient,
) *FavoriteItemService {
	return &FavoriteItemService{
		favoriteFolderRepo: favoriteFolderRepo,
		favoriteItemRepo:   favoriteItemRepo,
		favoriteStatsRepo:  favoriteStatsRepo,
		likeRepo:           likeRepo,
		userClient:         userClient,
		videoClient:        videoClient,
		novelClient:        novelClient,
		musicClient:        musicClient,
	}
}

// getContentInfoByType 根据内容类型从相应的服务获取内容信息
func (s *FavoriteItemService) getContentInfoByType(contentKSUID, contentType string) (*client.ContentInfo, *errors.Errors) {
	switch contentType {
	case "video", "anime", "short":
		// 视频、动漫、短视频都通过video服务处理
		content, err := s.videoClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	case "novel":
		// 小说通过novel服务处理
		content, err := s.novelClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	case "music":
		// 音乐通过music服务处理
		content, err := s.musicClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	default:
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("不支持的内容类型: %s", contentType))
	}
}

// AddToFavorite 添加到收藏夹
func (s *FavoriteItemService) AddToFavorite(ctx context.Context, userKSUID string, req *dto.AddToFavoriteRequest) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Strs("folder_ids", req.FavoriteFolderIDs).
		Msg("正在添加内容到收藏夹")

	// 根据内容类型验证内容是否存在
	content, gErr := s.getContentInfoByType(req.ContentKSUID, req.ContentType)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("内容不存在")
		return gErr
	}

	// 验证内容类型
	contentType := model.ContentType(req.ContentType)
	if !model.IsValidContentType(contentType) {
		if content != nil && content.ContentType != "" {
			contentType = model.ContentType(content.ContentType) // 使用内容服务返回的类型
		} else {
			return errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, repositoryErrors.ErrInvalidContentType)
		}
	}

	// 如果没有指定收藏夹，添加到默认收藏夹
	folderIDs := req.FavoriteFolderIDs
	if len(folderIDs) == 0 {
		folderIDs = []string{""} // 空字符串表示使用默认收藏夹
	}

	// 检查重复收藏
	nonDuplicateFolderIDs, err := s.checkDuplicateFavorites(ctx, userKSUID, req.ContentKSUID, folderIDs)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查重复收藏失败")
		return err
	}

	if len(nonDuplicateFolderIDs) == 0 {
		// 所有收藏夹都已存在该内容
		log.Info().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内容已在所有指定收藏夹中存在")
		return nil
	}

	// 检查用户是否已经收藏过这个内容（在任何收藏夹中）
	existingItems, checkErr := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	if checkErr != nil && checkErr != repositoryErrors.ErrFavoriteItemNotFound {
		log.Error().Err(checkErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查用户收藏状态失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, checkErr)
	}
	isFirstTimeFavorite := len(existingItems) == 0 || checkErr == repositoryErrors.ErrFavoriteItemNotFound

	// 批量添加到收藏夹
	for _, folderID := range nonDuplicateFolderIDs {
		// 创建收藏项
		item := model.NewFavoriteItem(userKSUID, req.ContentKSUID, model.ContentType(req.ContentType), folderID)
		err := s.favoriteItemRepo.BatchCreate(ctx, []*model.FavoriteItem{item})
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Str("folder_id", folderID).
				Msg("添加到收藏夹失败")
			return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
		}

		// 更新收藏夹的收藏项数量
		err = s.favoriteFolderRepo.UpdateItemCount(ctx, folderID, true)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Str("folder_id", folderID).
				Msg("更新收藏夹收藏项数量失败")
			// 不返回错误，因为收藏操作已经成功，计数更新失败不应该影响用户体验
		} else {
			log.Debug().
				Str("user_ksuid", userKSUID).
				Str("folder_id", folderID).
				Msg("收藏夹收藏项数量更新成功")
		}
	}

	// 如果是用户第一次收藏这个内容，更新收藏统计
	if isFirstTimeFavorite {
		err := s.favoriteStatsRepo.IncrementFavoriteCount(ctx, req.ContentKSUID, contentType)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("更新收藏统计失败")
			// 不返回错误，因为收藏操作已经成功，统计失败不应该影响用户体验
		} else {
			log.Debug().
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("收藏统计更新成功")
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Int("added_count", len(nonDuplicateFolderIDs)).
		Bool("is_first_time_favorite", isFirstTimeFavorite).
		Msg("内容添加到收藏夹成功")

	return nil
}

// RemoveFromFavorite 从收藏夹移除
func (s *FavoriteItemService) RemoveFromFavorite(ctx context.Context, userKSUID string, req *dto.RemoveFromFavoriteRequest) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Interface("folder_ids", req.FavoriteFolderIDs).
		Msg("正在从收藏夹移除内容")

	// 检查用户在删除前是否收藏了这个内容
	existingItems, checkErr := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	if checkErr != nil && checkErr != repositoryErrors.ErrFavoriteItemNotFound {
		log.Error().Err(checkErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查用户收藏状态失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, checkErr)
	}
	hadFavorites := len(existingItems) > 0 && checkErr != repositoryErrors.ErrFavoriteItemNotFound

	if !hadFavorites {
		log.Info().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("用户未收藏此内容，无需删除")
		return nil
	}

	var affectedFolderIDs []string
	var deleteErr error

	// 判断是从指定收藏夹删除还是从所有收藏夹删除
	if len(req.FavoriteFolderIDs) > 0 {
		// 从指定收藏夹删除
		log.Debug().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Interface("folder_ids", req.FavoriteFolderIDs).
			Msg("从指定收藏夹删除内容")

		// 验证指定的收藏夹中确实有这个内容
		validFolderIDs := make([]string, 0, len(req.FavoriteFolderIDs))
		for _, folderID := range req.FavoriteFolderIDs {
			for _, item := range existingItems {
				if item.FavoriteFolderID == folderID {
					validFolderIDs = append(validFolderIDs, folderID)
					break
				}
			}
		}

		if len(validFolderIDs) == 0 {
			log.Info().
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Interface("folder_ids", req.FavoriteFolderIDs).
				Msg("指定的收藏夹中没有此内容，无需删除")
			return nil
		}

		affectedFolderIDs = validFolderIDs
		deleteErr = s.favoriteItemRepo.DeleteByContentAndFolders(ctx, userKSUID, req.ContentKSUID, validFolderIDs)
	} else {
		// 从所有收藏夹删除
		log.Debug().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("从所有收藏夹删除内容")

		// 记录所有受影响的收藏夹ID
		for _, item := range existingItems {
			affectedFolderIDs = append(affectedFolderIDs, item.FavoriteFolderID)
		}

		deleteErr = s.favoriteItemRepo.DeleteByContentKSUID(ctx, userKSUID, req.ContentKSUID)
	}
	if deleteErr != nil {
		log.Error().Err(deleteErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("从收藏夹移除内容失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, deleteErr)
	}

	// 更新受影响收藏夹的收藏项数量
	for _, folderID := range affectedFolderIDs {
		err := s.favoriteFolderRepo.UpdateItemCount(ctx, folderID, false)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Str("folder_id", folderID).
				Msg("更新收藏夹收藏项数量失败")
			// 不返回错误，因为删除操作已经成功，计数更新失败不应该影响用户体验
		} else {
			log.Debug().
				Str("user_ksuid", userKSUID).
				Str("folder_id", folderID).
				Msg("收藏夹收藏项数量减少成功")
		}
	}

	// 检查用户是否还有其他收藏夹中的该内容，如果没有则更新收藏统计
	remainingItems, remainingErr := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	if remainingErr != nil && remainingErr != repositoryErrors.ErrFavoriteItemNotFound {
		log.Error().Err(remainingErr).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查剩余收藏状态失败")
		// 不返回错误，因为删除操作已经成功
	} else if len(remainingItems) == 0 || remainingErr == repositoryErrors.ErrFavoriteItemNotFound {
		// 用户完全取消收藏，减少统计
		err := s.favoriteStatsRepo.DecrementFavoriteCount(ctx, req.ContentKSUID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("更新收藏统计失败")
			// 不返回错误，因为删除操作已经成功，统计失败不应该影响用户体验
		} else {
			log.Debug().
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("收藏统计减少成功")
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Interface("folder_ids", req.FavoriteFolderIDs).
		Int("affected_folders", len(affectedFolderIDs)).
		Msg("内容从收藏夹移除成功")

	return nil
}

// MoveFavoriteItem 移动收藏项
func (s *FavoriteItemService) MoveFavoriteItem(ctx context.Context, userKSUID string, req *dto.MoveFavoriteItemRequest) (*dto.BatchFavoriteOperationResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("item_id", req.FavoriteItemID).
		Str("target_folder_id", req.TargetFolderID).
		Msg("正在移动收藏项")

	// 移动单个收藏项
	if req.FavoriteItemID != "" {
		// 先获取原始收藏项信息，以便知道源收藏夹ID
		originalItem, err := s.favoriteItemRepo.GetByID(ctx, req.FavoriteItemID, userKSUID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("item_id", req.FavoriteItemID).
				Msg("获取原始收藏项信息失败")
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}

		// 检查是否是同一个收藏夹，如果是则无需移动
		if originalItem.FavoriteFolderID == req.TargetFolderID {
			log.Info().
				Str("user_ksuid", userKSUID).
				Str("item_id", req.FavoriteItemID).
				Str("folder_id", req.TargetFolderID).
				Msg("收藏项已在目标收藏夹中，无需移动")
			return &dto.BatchFavoriteOperationResponse{
				Success:        true,
				TotalRequested: 1,
				SuccessCount:   0,
				SkippedCount:   1,
			}, nil
		}

		sourceFolderID := originalItem.FavoriteFolderID

		// 执行移动操作
		err = s.favoriteItemRepo.MoveTo(ctx, req.FavoriteItemID, userKSUID, req.TargetFolderID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("item_id", req.FavoriteItemID).
				Msg("移动收藏项失败")
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
		}

		// 更新源收藏夹的收藏项数量（减1）
		err = s.favoriteFolderRepo.UpdateItemCount(ctx, sourceFolderID, false)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("source_folder_id", sourceFolderID).
				Msg("更新源收藏夹收藏项数量失败")
			// 不返回错误，因为移动操作已经成功
		}

		// 更新目标收藏夹的收藏项数量（加1）
		err = s.favoriteFolderRepo.UpdateItemCount(ctx, req.TargetFolderID, true)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("target_folder_id", req.TargetFolderID).
				Msg("更新目标收藏夹收藏项数量失败")
			// 不返回错误，因为移动操作已经成功
		}

		log.Info().
			Str("user_ksuid", userKSUID).
			Str("item_id", req.FavoriteItemID).
			Str("source_folder_id", sourceFolderID).
			Str("target_folder_id", req.TargetFolderID).
			Msg("收藏项移动成功")

		return &dto.BatchFavoriteOperationResponse{
			Success:        true,
			TotalRequested: 1,
			SuccessCount:   1,
			SkippedCount:   0,
		}, nil
	}

	// 批量移动收藏项
	if len(req.TargetFolderIDs) > 0 {
		// 这里需要实现批量移动逻辑
		// 暂时返回错误，需要扩展repository接口
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("batch move not implemented yet"))
	}

	return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("no items specified for move operation"))
}

// ManageFavorite 管理收藏（合并添加和删除功能）
func (s *FavoriteItemService) ManageFavorite(ctx context.Context, userKSUID string, req *dto.ManageFavoriteRequest) (*dto.BatchFavoriteOperationResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Strs("add_folder_ids", req.AddFavoriteFolderIDs).
		Strs("del_folder_ids", req.DelFavoriteFolderIDs).
		Msg("正在管理收藏")

	var totalRequested, successCount, skippedCount int
	var isFirstTimeFavorite bool

	// 根据内容类型验证内容是否存在
	content, gErr := s.getContentInfoByType(req.ContentKSUID, req.ContentType)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("内容不存在")
		return nil, gErr
	}

	// 验证内容类型
	contentType := model.ContentType(req.ContentType)
	if !model.IsValidContentType(contentType) {
		if content != nil && content.ContentType != "" {
			contentType = model.ContentType(content.ContentType) // 使用内容服务返回的类型
		} else {
			return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, repositoryErrors.ErrInvalidContentType)
		}
	}

	// 检查用户在操作前是否收藏了这个内容
	existingItems, checkErr := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	wasAlreadyFavorited := checkErr == nil && len(existingItems) > 0

	// 处理删除操作
	if len(req.DelFavoriteFolderIDs) > 0 {
		totalRequested += len(req.DelFavoriteFolderIDs)

		// 获取实际需要删除的收藏夹ID（只删除存在的）
		var actualDelFolderIDs []string
		if checkErr == nil {
			existingFolderMap := make(map[string]bool)
			for _, item := range existingItems {
				existingFolderMap[item.FavoriteFolderID] = true
			}

			for _, folderID := range req.DelFavoriteFolderIDs {
				if existingFolderMap[folderID] {
					actualDelFolderIDs = append(actualDelFolderIDs, folderID)
				} else {
					skippedCount++ // 不存在的收藏项，跳过
				}
			}
		} else {
			// 如果检查失败，说明没有收藏项，全部跳过
			skippedCount += len(req.DelFavoriteFolderIDs)
		}

		// 执行删除操作
		if len(actualDelFolderIDs) > 0 {
			err := s.favoriteItemRepo.DeleteByContentAndFolders(ctx, userKSUID, req.ContentKSUID, actualDelFolderIDs)
			if err != nil {
				log.Error().Err(err).
					Str("user_ksuid", userKSUID).
					Str("content_ksuid", req.ContentKSUID).
					Strs("folder_ids", actualDelFolderIDs).
					Msg("从收藏夹删除内容失败")
				return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
			}

			successCount += len(actualDelFolderIDs)

			// 更新收藏夹的收藏项数量
			for _, folderID := range actualDelFolderIDs {
				err := s.favoriteFolderRepo.UpdateItemCount(ctx, folderID, false)
				if err != nil {
					log.Error().Err(err).
						Str("user_ksuid", userKSUID).
						Str("folder_id", folderID).
						Msg("更新收藏夹收藏项数量失败")
					// 不返回错误，因为删除操作已经成功
				}
			}

			log.Info().
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Strs("deleted_folder_ids", actualDelFolderIDs).
				Msg("从收藏夹删除内容成功")
		}
	}

	// 处理添加操作
	if len(req.AddFavoriteFolderIDs) > 0 {
		totalRequested += len(req.AddFavoriteFolderIDs)

		// 如果没有指定收藏夹，添加到默认收藏夹
		folderIDs := req.AddFavoriteFolderIDs
		if len(folderIDs) == 0 {
			folderIDs = []string{""} // 空字符串表示使用默认收藏夹
		}

		// 检查重复收藏（只添加不存在的）
		var actualAddFolderIDs []string
		if checkErr == nil {
			existingFolderMap := make(map[string]bool)
			for _, item := range existingItems {
				existingFolderMap[item.FavoriteFolderID] = true
			}

			for _, folderID := range folderIDs {
				if !existingFolderMap[folderID] {
					actualAddFolderIDs = append(actualAddFolderIDs, folderID)
				} else {
					skippedCount++ // 已存在的收藏项，跳过
				}
			}
		} else {
			// 如果检查失败，说明没有收藏项，全部添加
			actualAddFolderIDs = folderIDs
		}

		// 检查是否是第一次收藏这个内容
		if !wasAlreadyFavorited && len(actualAddFolderIDs) > 0 {
			isFirstTimeFavorite = true
		}

		// 执行添加操作
		if len(actualAddFolderIDs) > 0 {
			// 处理默认收藏夹
			for i, folderID := range actualAddFolderIDs {
				if folderID == "" {
					defaultFolder, err := s.favoriteFolderRepo.GetDefaultFolder(ctx, userKSUID)
					if err != nil {
						log.Error().Err(err).
							Str("user_ksuid", userKSUID).
							Msg("获取默认收藏夹失败")
						return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
					}
					actualAddFolderIDs[i] = defaultFolder.FavoriteFolderID
				}
			}

			// 批量添加到收藏夹
			var items []*model.FavoriteItem
			for _, folderID := range actualAddFolderIDs {
				item := model.NewFavoriteItem(userKSUID, req.ContentKSUID, contentType, folderID)
				items = append(items, item)
			}

			err := s.favoriteItemRepo.BatchCreate(ctx, items)
			if err != nil {
				log.Error().Err(err).
					Str("user_ksuid", userKSUID).
					Str("content_ksuid", req.ContentKSUID).
					Strs("folder_ids", actualAddFolderIDs).
					Msg("批量添加到收藏夹失败")
				return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
			}

			successCount += len(actualAddFolderIDs)

			// 更新收藏夹的收藏项数量
			for _, folderID := range actualAddFolderIDs {
				err := s.favoriteFolderRepo.UpdateItemCount(ctx, folderID, true)
				if err != nil {
					log.Error().Err(err).
						Str("user_ksuid", userKSUID).
						Str("folder_id", folderID).
						Msg("更新收藏夹收藏项数量失败")
					// 不返回错误，因为添加操作已经成功
				}
			}

			log.Info().
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Strs("added_folder_ids", actualAddFolderIDs).
				Msg("批量添加到收藏夹成功")
		}
	}

	// 检查用户在操作后是否还有收藏这个内容
	finalItems, finalCheckErr := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	isStillFavorited := finalCheckErr == nil && len(finalItems) > 0

	// 更新收藏统计
	if isFirstTimeFavorite {
		// 第一次收藏，增加统计
		err := s.favoriteStatsRepo.IncrementFavoriteCount(ctx, req.ContentKSUID, contentType)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("增加收藏统计失败")
		}
	} else if wasAlreadyFavorited && !isStillFavorited {
		// 之前收藏了，现在完全取消收藏，减少统计
		err := s.favoriteStatsRepo.DecrementFavoriteCount(ctx, req.ContentKSUID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Msg("减少收藏统计失败")
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Int("total_requested", totalRequested).
		Int("success_count", successCount).
		Int("skipped_count", skippedCount).
		Bool("is_first_time_favorite", isFirstTimeFavorite).
		Bool("was_already_favorited", wasAlreadyFavorited).
		Bool("is_still_favorited", isStillFavorited).
		Msg("管理收藏完成")

	return &dto.BatchFavoriteOperationResponse{
		Success:        true,
		TotalRequested: totalRequested,
		SuccessCount:   successCount,
		SkippedCount:   skippedCount,
	}, nil
}

// GetFavoriteItems 获取收藏项列表
func (s *FavoriteItemService) GetFavoriteItems(ctx context.Context, currentUserKSUID string, req *dto.GetFavoriteItemsRequest) (*dto.GetFavoriteItemsResponse, *errors.Errors) {
	log.Info().
		Str("current_user_ksuid", currentUserKSUID).
		Str("folder_id", req.FavoriteFolderID).
		Str("content_type", req.ContentType).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("正在获取收藏项列表")

	var items []*model.FavoriteItem
	var total int64
	var err error

	folderInfo, err := s.favoriteFolderRepo.GetByFolderKSUID(ctx, req.FavoriteFolderID)
	if err != nil {
		log.Error().Err(err).
			Str("current_user_ksuid", currentUserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Str("content_type", req.ContentType).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Msg("获取收藏项列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if !folderInfo.IsPublic && folderInfo.UserKSUID != currentUserKSUID {
		log.Error().
			Str("current_user_ksuid", currentUserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Str("content_type", req.ContentType).
			Msg("BYD,想看别人私有收藏夹")
		return nil, errors.NewGlobalErrors(errors.PERMISSION_DENIED, errors.PERMISSION_DENIED, fmt.Errorf("permission denied"))
	}

	// 根据查询条件获取收藏项
	if req.ContentType != "" {
		// 按收藏夹和内容类型查询
		items, total, err = s.favoriteItemRepo.GetByFolderAndContentType(ctx, folderInfo.UserKSUID, req.FavoriteFolderID, model.ContentType(req.ContentType), req.Page, req.PageSize)
	} else {
		// 按收藏夹查询
		items, total, err = s.favoriteItemRepo.GetByFolderKSUID(ctx, folderInfo.UserKSUID, req.FavoriteFolderID, req.Page, req.PageSize)
	}

	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("获取收藏项列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 批量获取内容信息
	contentInfoMap, err := s.batchGetContentInfo(ctx, items)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("批量获取内容信息失败")
		// 不返回错误，继续处理，只是没有内容详情
	}

	// 批量获取点赞统计信息
	likeStatsMap, err := s.batchGetLikeStats(ctx, items)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("批量获取点赞统计信息失败")
		// 不返回错误，继续处理，只是没有点赞统计
	}

	// 转换为响应格式
	var itemResponses []*dto.FavoriteItemResponse
	for _, item := range items {
		itemResponse := s.toItemResponse(item)
		// 添加内容信息
		if contentInfoMap != nil {
			if contentInfo, exists := contentInfoMap[item.ContentKSUID]; exists {
				itemResponse.ContentInfo = &dto.ContentInfo{
					UserKSUID:    contentInfo.UserKSUID,
					ContentKSUID: contentInfo.ContentKSUID,
					Title:        contentInfo.Title,
					CoverURL:     contentInfo.CoverURL,
					Duration:     contentInfo.Duration,
					ViewCount:    contentInfo.ViewCount,
					LikeCount:    0, // 初始化为0，后面会被覆盖
					DislikeCount: 0, // 初始化为0，后面会被覆盖
					CommentCount: contentInfo.CommentCount,
				}

				// 用本服务的点赞统计覆盖 video 服务的点赞数据
				if likeStatsMap != nil {
					if likeStats, exists := likeStatsMap[item.ContentKSUID]; exists {
						itemResponse.ContentInfo.LikeCount = likeStats.LikeCount
						itemResponse.ContentInfo.DislikeCount = likeStats.DislikeCount
					}
				}
			}
		}
		itemResponses = append(itemResponses, itemResponse)
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	response := &dto.GetFavoriteItemsResponse{
		Items:      itemResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	log.Info().
		Str("user_ksuid", folderInfo.UserKSUID).
		Int64("total", total).
		Int("returned", len(itemResponses)).
		Msg("收藏项列表获取成功")

	return response, nil
}

// ===== 辅助方法 =====

// checkDuplicateFavorites 检查重复收藏
func (s *FavoriteItemService) checkDuplicateFavorites(ctx context.Context, userKSUID, contentKSUID string, folderIDs []string) ([]string, *errors.Errors) {
	// 获取现有的收藏项
	existingItems, err := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, contentKSUID)
	if err != nil && err != repositoryErrors.ErrFavoriteItemNotFound {
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 创建现有收藏夹ID的映射
	existingFolderIDs := make(map[string]bool)
	for _, item := range existingItems {
		existingFolderIDs[item.FavoriteFolderID] = true
	}

	var nonDuplicateIDs []string
	for _, folderID := range folderIDs {
		// 如果这个收藏夹中还没有收藏这个内容，则可以添加
		if !existingFolderIDs[folderID] {
			nonDuplicateIDs = append(nonDuplicateIDs, folderID)
		}
	}

	return nonDuplicateIDs, nil
}

// batchGetContentInfo 批量获取内容信息
func (s *FavoriteItemService) batchGetContentInfo(ctx context.Context, items []*model.FavoriteItem) (map[string]*client.ContentInfo, *errors.Errors) {
	if len(items) == 0 {
		return make(map[string]*client.ContentInfo), nil
	}

	// 按内容类型分组收集内容KSUID
	videoKSUIDs := make([]string, 0)
	novelKSUIDs := make([]string, 0)
	musicKSUIDs := make([]string, 0)

	for _, item := range items {
		switch item.ContentType {
		case "video", "anime", "short":
			videoKSUIDs = append(videoKSUIDs, item.ContentKSUID)
		case "novel":
			novelKSUIDs = append(novelKSUIDs, item.ContentKSUID)
		case "music":
			musicKSUIDs = append(musicKSUIDs, item.ContentKSUID)
		}
	}

	// 合并所有内容信息
	allContentInfo := make(map[string]*client.ContentInfo)

	// 批量获取视频内容信息
	if len(videoKSUIDs) > 0 {
		videoContentInfo, err := s.videoClient.BatchGetContentsByKSUIDs(videoKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("video_ksuids", videoKSUIDs).
				Msg("批量获取视频内容信息失败")
		} else {
			for k, v := range videoContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	// 批量获取小说内容信息
	if len(novelKSUIDs) > 0 {
		novelContentInfo, err := s.novelClient.BatchGetContentsByKSUIDs(novelKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("novel_ksuids", novelKSUIDs).
				Msg("批量获取小说内容信息失败")
		} else {
			for k, v := range novelContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	// 批量获取音乐内容信息
	if len(musicKSUIDs) > 0 {
		musicContentInfo, err := s.musicClient.BatchGetContentsByKSUIDs(musicKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("music_ksuids", musicKSUIDs).
				Msg("批量获取音乐内容信息失败")
		} else {
			for k, v := range musicContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	log.Info().
		Int("total_items", len(items)).
		Int("video_count", len(videoKSUIDs)).
		Int("novel_count", len(novelKSUIDs)).
		Int("music_count", len(musicKSUIDs)).
		Int("content_info_count", len(allContentInfo)).
		Msg("批量获取内容信息完成")

	return allContentInfo, nil
}

// batchGetLikeStats 批量获取点赞统计信息
func (s *FavoriteItemService) batchGetLikeStats(ctx context.Context, items []*model.FavoriteItem) (map[string]repository.LikeStatsItem, *errors.Errors) {
	if len(items) == 0 {
		return make(map[string]repository.LikeStatsItem), nil
	}

	// 收集所有内容KSUID
	contentKSUIDs := make([]string, 0, len(items))
	for _, item := range items {
		contentKSUIDs = append(contentKSUIDs, item.ContentKSUID)
	}

	// 批量获取点赞统计
	likeStatsMap, err := s.likeRepo.GetContentLikeStats(ctx, contentKSUIDs)
	if err != nil {
		log.Error().Err(err).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量获取点赞统计信息失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	log.Info().
		Int("total_items", len(items)).
		Int("like_stats_count", len(likeStatsMap)).
		Msg("批量获取点赞统计信息完成")

	return likeStatsMap, nil
}

// toItemResponse 转换收藏项模型为响应格式
func (s *FavoriteItemService) toItemResponse(item *model.FavoriteItem) *dto.FavoriteItemResponse {
	return &dto.FavoriteItemResponse{
		FavoriteItemID:   item.FavoriteItemID,
		UserKSUID:        item.UserKSUID,
		ContentKSUID:     item.ContentKSUID,
		ContentType:      string(item.ContentType),
		FavoriteFolderID: item.FavoriteFolderID,
		CreatedAt:        item.CreatedAt,
		UpdatedAt:        item.UpdatedAt,
	}
}

// GetTopFavoritedContent 获取最受欢迎的内容
func (s *FavoriteItemService) GetTopFavoritedContent(ctx context.Context, contentType string, limit int) (*dto.GetTopFavoritedContentResponse, *errors.Errors) {
	log.Debug().
		Str("content_type", contentType).
		Int("limit", limit).
		Msg("开始获取最受欢迎的内容")

	var modelContentType model.ContentType
	if contentType != "" {
		modelContentType = model.ContentType(contentType)
		if !model.IsValidContentType(modelContentType) {
			return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("无效的内容类型: %s", contentType))
		}
	}

	statsList, err := s.favoriteStatsRepo.GetTopFavoritedContent(ctx, modelContentType, limit)
	if err != nil {
		log.Error().Err(err).
			Str("content_type", contentType).
			Int("limit", limit).
			Msg("获取最受欢迎的内容失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	contents := make([]*dto.ContentFavoriteStatsResponse, 0, len(statsList))
	for _, stats := range statsList {
		contents = append(contents, &dto.ContentFavoriteStatsResponse{
			ContentKSUID:  stats.ContentKSUID,
			ContentType:   string(stats.ContentType),
			FavoriteCount: stats.FavoriteCount,
			CreatedAt:     stats.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     stats.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return &dto.GetTopFavoritedContentResponse{
		Contents: contents,
	}, nil
}
