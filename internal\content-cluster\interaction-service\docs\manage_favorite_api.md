# 管理收藏API文档

## 概述

新增的管理收藏API将原来的"添加到收藏夹"和"移动收藏项"功能合并为一个接口，通过前端传递的`add_favorite_folder_ids`和`del_favorite_folder_ids`来判断增加和删除操作。

## API接口

### 管理收藏

**接口地址**: `PUT /api/v1/favorites/items/manage`

**请求方法**: PUT

**需要认证**: 是

**请求参数**:

```json
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "add_favorite_folder_ids": ["folder_id_1", "folder_id_2"],
  "del_favorite_folder_ids": ["folder_id_3", "folder_id_4"]
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| content_ksuid | string | 是 | 内容ID |
| content_type | string | 是 | 内容类型（video/novel/music） |
| add_favorite_folder_ids | []string | 否 | 需要添加到的收藏夹ID数组 |
| del_favorite_folder_ids | []string | 否 | 需要从中删除的收藏夹ID数组 |

**注意事项**:
- `add_favorite_folder_ids`和`del_favorite_folder_ids`至少要有一个不为空
- 如果`add_favorite_folder_ids`为空数组，表示不进行添加操作
- 如果`del_favorite_folder_ids`为空数组，表示不进行删除操作

**响应示例**:

```json
{
  "code": 20000,
  "message": "success",
  "data": {
    "success": true,
    "total_requested": 4,
    "success_count": 3,
    "skipped_count": 1
  }
}
```

**响应字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 操作是否成功 |
| total_requested | int | 请求处理的总数 |
| success_count | int | 成功处理的数量 |
| skipped_count | int | 跳过的数量 |

## 业务逻辑

### 添加收藏逻辑
- 如果内容已经在指定收藏夹中，则跳过（不报错）
- 如果内容不在指定收藏夹中，则添加
- 如果`add_favorite_folder_ids`为空，则不进行添加操作

### 删除收藏逻辑
- 如果内容在指定收藏夹中，则删除
- 如果内容不在指定收藏夹中，则跳过（不报错）
- 如果`del_favorite_folder_ids`为空，则不进行删除操作

### 统计更新逻辑
- 如果用户第一次收藏该内容，收藏统计+1
- 如果用户完全取消收藏该内容（所有收藏夹都删除），收藏统计-1
- 其他情况统计不变

## 性能优化

### 数据库操作优化
- 批量操作减少数据库访问次数
- 只对实际需要操作的收藏夹进行数据库操作
- 跳过重复操作，避免不必要的数据库写入

### 错误处理
- 统计更新失败不影响收藏操作的成功
- 收藏夹数量更新失败不影响收藏操作的成功
- 详细的日志记录便于问题排查

## 使用示例

### 示例1：添加到多个收藏夹
```json
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "add_favorite_folder_ids": ["folder_1", "folder_2", "folder_3"],
  "del_favorite_folder_ids": []
}
```

### 示例2：从多个收藏夹删除
```json
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "add_favorite_folder_ids": [],
  "del_favorite_folder_ids": ["folder_1", "folder_2"]
}
```

### 示例3：同时添加和删除
```json
{
  "content_ksuid": "01ARZ3NDEKTSV4RRFFQ69G5FAV",
  "content_type": "video",
  "add_favorite_folder_ids": ["folder_3", "folder_4"],
  "del_favorite_folder_ids": ["folder_1", "folder_2"]
}
```

## 与原有API的对比

| 功能 | 原有API | 新API |
|------|---------|-------|
| 添加收藏 | POST /api/v1/favorites/items | PUT /api/v1/favorites/items/manage |
| 删除收藏 | DELETE /api/v1/favorites/items | PUT /api/v1/favorites/items/manage |
| 移动收藏 | PUT /api/v1/favorites/items/move | PUT /api/v1/favorites/items/manage |
| 批量操作 | 需要多次调用 | 一次调用完成 |
| 性能 | 多次数据库操作 | 优化的批量操作 |

## 兼容性说明

- 原有的API接口保持不变，可以继续使用
- 新API是对原有功能的增强和优化
- 建议新的前端实现使用新API以获得更好的性能
